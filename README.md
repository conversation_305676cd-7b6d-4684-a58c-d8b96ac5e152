# LBCDev Ecommerce

Un paquete modular de comercio electrónico para Laravel que proporciona una solución flexible y desacoplada para manejar carritos, productos, variantes, impuestos, stock, y categorización por taxonomías.

---

## Índice

-  [Introducción](#introducción)
-  [Instalación](#instalación)
-  [Configuración](#configuración)
   -  [Publicar archivos](#publicar-archivos)
   -  [Configurar opciones](#configurar-opciones)
-  [Migraciones](#migraciones)
-  [Modelos y relaciones](#modelos-y-relaciones)
   -  [Producto](#producto)
   -  [Variantes](#variantes)
   -  [Carrito](#carrito)
   -  [Ítems del carrito](#ítems-del-carrito)
-  [Stock e inventario](#stock-e-inventario)
-  [Precios y SKUs](#precios-y-skus)
-  [Taxonomías](#taxonomías)
   -  [Uso del paquete `aliziodev/laravel-taxonomy`](#uso-del-paquete-aliziodevlaravel-taxonomy)
   -  [Trait `HasCategories`](#trait-hascategories)
-  [Eventos](#eventos)
-  [Comandos Artisan](#comandos-artisan)
-  [Extensión y personalización](#extensión-y-personalización)
-  [Pruebas](#pruebas)
-  [Contribuciones](#contribuciones)
-  [Licencia](#licencia)

---

## Introducción

`lbcdev/ecommerce` es un paquete para Laravel que proporciona las funcionalidades básicas necesarias para implementar una tienda en línea con un diseño desacoplado y extensible.

El paquete incluye:

-  Carrito persistente con soporte multi-guard y anónimo
-  Modelo de productos y variantes
-  Control de stock básico
-  Precios e impuestos por ítem
-  SKU obligatorio y heredado
-  Integración opcional con taxonomías (`aliziodev/laravel-taxonomy`)
-  Eventos personalizables
-  Comandos artisan útiles

---

## Instalación

### 1. Requisitos

-  Laravel 10+
-  PHP 8.1+
-  Base de datos MySQL, PostgreSQL u otra compatible con Laravel

### 2. Instalar el paquete principal

```bash
composer require lbcdev/ecommerce
```

Esto instalará también la dependencia [aliziodev/laravel-taxonomy](https://github.com/aliziodev/laravel-taxonomy)

### 3. Publicar archivos del paquete

```bash
php artisan vendor:publish --provider="LBCDev\Ecommerce\EcommerceServiceProvider"
```

ó

```bash
php artisan vendor:publish --tag="lbcdev-ecommerce-config"
php artisan vendor:publish --tag="lbcdev-ecommerce-views"
php artisan vendor:publish --tag="lbcdev-ecommerce-assets"
```

### 4. Ejecutar migraciones

#### Migraciones de ecommerce:

```bash
php artisan migrate
```

| 📌 Importante: debes migrar las tablas del ecommerce y de las taxonomías por separado.

#### Migraciones de taxonomías (si usarás taxonomías):

```bash
php artisan vendor:publish --provider="AlizioDev\LaravelTaxonomy\TaxonomyServiceProvider" --tag="migrations"
php artisan migrate
```

#### 🧩 Compatibilidad con taxonomías

Este paquete depende del paquete aliziodev/laravel-taxonomy, pero para garantizar compatibilidad con proyectos existentes, puedes:

Opción A: Ya tienes una tabla taxonomies
No es necesario crearla. Simplemente asegúrate de que contenga los siguientes campos mínimos:

-  id
-  name
-  slug
-  type
-  parent_id
-  timestamps

Opción B: Crear la tabla con la migración incluida
Publica la migración condicional si no tienes una tabla aún:

```bash
php artisan vendor:publish --tag="lbcdev-ecommerce-migrations"
```

Y luego:

```bash
php artisan migrate
```

#### API de HasTaxonomy

```php
<?php

// Métodos estáticos
public static function bootHasTaxonomy(): void

// Relaciones
public function taxonomies(string $name = 'taxonomable'): MorphToMany
public function taxonomiesOfType(string|TaxonomyType $type, string $name = 'taxonomable'): Collection

// Operaciones de taxonomías
public function attachTaxonomies($taxonomies, string $name = 'taxonomable'): self
public function detachTaxonomies($taxonomies = null, string $name = 'taxonomable'): self
public function syncTaxonomies($taxonomies, string $name = 'taxonomable'): self
public function toggleTaxonomies($taxonomies, string $name = 'taxonomable'): self

// Verificaciones
public function hasTaxonomies($taxonomies, string $name = 'taxonomable'): bool
public function hasAllTaxonomies($taxonomies, string $name = 'taxonomable'): bool
public function hasTaxonomyType(string|TaxonomyType $type, string $name = 'taxonomable'): bool
public function hasAncestorTaxonomy(int $taxonomyId): bool
public function hasDescendantTaxonomy(int $taxonomyId): bool

// Scopes de consulta
public function scopeWithAnyTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithAllTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomyType(Builder $query, string|TaxonomyType $type, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomy(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeWithoutTaxonomies(Builder $query, $taxonomies, string $name = 'taxonomable'): Builder
public function scopeFilterByTaxonomies(Builder $query, array $filters, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomySlug(Builder $query, string $slug, string|TaxonomyType|null $type = null, string $name = 'taxonomable'): Builder
public function scopeWithTaxonomyHierarchy(Builder $query, int $taxonomyId, bool $includeDescendants = true): Builder
public function scopeWithTaxonomyAtDepth(Builder $query, int $depth, string|TaxonomyType|null $type = null): Builder

// Métodos jerárquicos
public function getHierarchicalTaxonomies(string|TaxonomyType|null $type = null): Collection
public function getAncestorTaxonomies(string|TaxonomyType|null $type = null): Collection

// Método protegido utilitario
protected function getTaxonomyIds($taxonomies): array
```
