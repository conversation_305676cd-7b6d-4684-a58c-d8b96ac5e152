<?php

namespace Tests\Unit\Commands;

use Tests\TestCase;
use Illuminate\Support\Facades\File;
use LBCDev\Ecommerce\Console\Commands\GenerateConfigDocs;
use LBCDev\Ecommerce\Console\Commands\GenerateEventsDocs;
use LBCDev\Ecommerce\Console\Commands\GenerateContractsDocs;

class GenerateDocsCommandsTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure docs directory exists
        File::ensureDirectoryExists(base_path('docs/reference'));
        
        // Create a mock config file for testing
        File::ensureDirectoryExists(config_path());
        File::put(config_path('lbcdev-ecommerce.php'), $this->getMockConfig());
    }

    protected function tearDown(): void
    {
        // Clean up test files
        $testFiles = [
            base_path('docs/reference/config.md'),
            base_path('docs/reference/events.md'),
            base_path('docs/reference/contracts.md'),
            config_path('lbcdev-ecommerce.php'),
        ];

        foreach ($testFiles as $file) {
            if (File::exists($file)) {
                File::delete($file);
            }
        }

        parent::tearDown();
    }

    public function test_generate_config_docs_command()
    {
        $this->artisan('lbcdev:docs-config')
            ->expectsOutput('Generating configuration documentation...')
            ->assertExitCode(0);

        $this->assertTrue(File::exists(base_path('docs/reference/config.md')));
        
        $content = File::get(base_path('docs/reference/config.md'));
        $this->assertStringContainsString('# Configuración del Paquete', $content);
        $this->assertStringContainsString('enabled', $content);
        $this->assertStringContainsString('currency', $content);
    }

    public function test_generate_events_docs_command()
    {
        $this->artisan('lbcdev:docs-events')
            ->expectsOutput('Generating events documentation...')
            ->assertExitCode(0);

        $this->assertTrue(File::exists(base_path('docs/reference/events.md')));
        
        $content = File::get(base_path('docs/reference/events.md'));
        $this->assertStringContainsString('# Sistema de Eventos', $content);
    }

    public function test_generate_contracts_docs_command()
    {
        $this->artisan('lbcdev:docs-contracts')
            ->expectsOutput('Generating contracts documentation...')
            ->assertExitCode(0);

        $this->assertTrue(File::exists(base_path('docs/reference/contracts.md')));
        
        $content = File::get(base_path('docs/reference/contracts.md'));
        $this->assertStringContainsString('# Contratos e Interfaces', $content);
    }

    public function test_config_docs_command_fails_without_config_file()
    {
        // Remove the config file
        File::delete(config_path('lbcdev-ecommerce.php'));

        $this->artisan('lbcdev:docs-config')
            ->expectsOutput('Configuration file not found at: ' . config_path('lbcdev-ecommerce.php'))
            ->assertExitCode(1);
    }

    private function getMockConfig(): string
    {
        return '<?php

return [
    "enabled" => true,
    "currency" => env("ECOMMERCE_CURRENCY", "EUR"),
    "tax_rate" => env("ECOMMERCE_TAX_RATE", 21.0),
    "order_number_prefix" => env("ECOMMERCE_ORDER_PREFIX", "ORD"),
    "cart" => [
        "driver" => env("ECOMMERCE_CART_DRIVER", "hybrid"),
        "session_key" => "ecommerce_cart",
        "expires_after_days" => env("ECOMMERCE_CART_EXPIRES_DAYS", 30),
        "auto_cleanup" => env("ECOMMERCE_CART_AUTO_CLEANUP", true),
    ],
    "send_order_confirmation" => env("ECOMMERCE_SEND_ORDER_CONFIRMATION", true),
    "admin_email" => env("ECOMMERCE_ADMIN_EMAIL"),
    "categories_taxonomy_type" => "lbcdev-ecommerce-category",
    "tags_taxonomy_type" => "lbcdev-ecommerce-tag",
    "categories_table" => "taxonomies",
    "taxonomy_slug_prefixes" => [
        "category" => "cat--",
        "tag" => "tag--",
    ],
    "models" => [],
];';
    }
}
