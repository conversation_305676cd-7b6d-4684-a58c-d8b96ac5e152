# LBCDev Ecommerce - Documentación

Documentación completa del paquete Laravel para funcionalidades de e-commerce.

## 🚀 Inicio Rápido

-  **[Instalación](getting-started/install.md)** - Cómo instalar y configurar el paquete
-  **[Guía de inicio](getting-started/quickstart.md)** - Primeros pasos y ejemplo básico

## 🏗️ Arquitectura

-  **[Visión general](architecture/overview.md)** - Arquitectura del sistema y módulos
-  **[Glosario de dominio](architecture/domain-glossary.md)** - Términos y conceptos clave
-  **[Diagramas](architecture/diagrams/)** - Diagramas de arquitectura y dependencias

## 📚 Referencia

-  **[Configuración](reference/config.md)** - Opciones de configuración disponibles
-  **[Eventos](reference/events.md)** - Sistema de eventos del paquete
-  **[Contratos](reference/contracts.md)** - Interfaces y contratos públicos
-  **[Base de datos](reference/database.md)** - Esquema de base de datos y migraciones
-  **[API](reference/api/)** - Documentación de endpoints HTTP

## 📖 Guías

-  **[Sobrescribir vistas](guides/override-views.md)** - Personalizar las vistas del paquete
-  **[Métodos de pago personalizados](guides/custom-payments.md)** - Integrar nuevos métodos de pago
-  **[Promociones y descuentos](guides/promotions.md)** - Sistema de promociones
-  **[Multi-tenant](guides/multi-tenant.md)** - Configuración para múltiples inquilinos

## 🔧 Decisiones de Arquitectura

-  **[ADRs](adr/)** - Registro de decisiones arquitectónicas importantes

---

## Estado de la Documentación

> 📝 **Nota**: Esta documentación se genera automáticamente desde el código fuente.
>
> **Última actualización**: _Se actualizará automáticamente con CI_
>
> **Cobertura**:
>
> -  ✅ Configuración (generada automáticamente)
> -  ✅ Eventos (generada automáticamente)
> -  ✅ Contratos (generada automáticamente)
> -  🔄 API HTTP (en desarrollo)
> -  📝 Guías (escritas manualmente)

## Contribuir a la Documentación

Para mantener la documentación actualizada:

1. **Documentación generada**: Se actualiza automáticamente ejecutando `composer docs:build`
2. **Guías manuales**: Edita los archivos en `docs/guides/`
3. **Decisiones arquitectónicas**: Añade nuevos ADRs en `docs/adr/`

```bash
# Regenerar toda la documentación
composer docs:build

# Solo generar documentación de configuración
composer docs:config

# Solo generar documentación de eventos
composer docs:events
```
