# Glosario de Dominio

Este glosario define los términos clave utilizados en el paquete LBCDev Ecommerce para evitar ambigüedades y mantener un lenguaje ubicuo.

## 📦 Catálogo y Productos

### **Producto (Product)**

Entidad principal que representa un artículo vendible. Un producto puede tener múltiples variantes.

**Propiedades clave:**

-  `name`: Nombre del producto
-  `description`: Descripción detallada
-  `price`: Precio base (sin impuestos)
-  `tax_rate`: Porcentaje de impuesto aplicable
-  `is_active`: Estado de disponibilidad

**Ejemplo:** "Camiseta Básica" es un producto que puede tener variantes de diferentes tallas y colores.

### **Variante (ProductVariant)**

Versión específica de un producto con características únicas (talla, color, etc.).

**Propiedades clave:**

-  `sku`: Código único de identificación (obligatorio)
-  `price`: Precio específico de la variante (puede diferir del producto base)
-  `stock_quantity`: Cantidad disponible en inventario
-  `attributes`: Características específicas (JSON)

**Ejemplo:** "Camiseta Básica - Talla M - Color Rojo" con SKU "SHIRT-M-RED-001"

### **SKU (Stock Keeping Unit)**

Código único alfanumérico que identifica inequívocamente una variante de producto.

**Reglas:**

-  Obligatorio para todas las variantes
-  Debe ser único en todo el sistema
-  Se hereda del producto padre si no se especifica

### **Linkable (ProductLinkable)**

Sistema polimórfico que permite asociar productos con cualquier modelo del sistema.

**Casos de uso:**

-  Productos relacionados
-  Productos recomendados
-  Bundles o paquetes
-  Accesorios

## 🛒 Carrito y Compras

### **Carrito (Cart)**

Contenedor temporal de productos seleccionados por un usuario antes de realizar la compra.

**Tipos de carrito:**

-  **Anónimo**: Asociado a una sesión sin usuario autenticado
-  **Persistente**: Asociado a un usuario registrado
-  **Híbrido**: Combina ambos enfoques

### **Item del Carrito (CartItem)**

Entrada individual dentro de un carrito que representa una variante específica y su cantidad.

**Propiedades:**

-  `variant_id`: Referencia a la variante del producto
-  `quantity`: Cantidad seleccionada
-  `unit_price`: Precio unitario al momento de añadir al carrito
-  `total_price`: Precio total (unit_price × quantity)

### **Checkout**

Proceso de conversión del carrito en un pedido confirmado, incluyendo datos de envío, facturación y pago.

## 📋 Pedidos y Cumplimiento

### **Pedido (Order)**

Solicitud formal de compra confirmada por el cliente.

**Estados del pedido:**

-  `PENDING`: Pedido creado, pendiente de procesamiento
-  `PROCESSING`: En proceso de preparación
-  `SHIPPED`: Enviado al cliente
-  `DELIVERED`: Entregado exitosamente
-  `CANCELLED`: Cancelado antes del envío
-  `REFUNDED`: Reembolsado total o parcialmente

### **Línea de Pedido (OrderItem)**

Entrada individual dentro de un pedido que representa una variante específica y su cantidad confirmada.

**Diferencia con CartItem:** Una vez creado el pedido, los OrderItems son inmutables y reflejan el estado exacto al momento de la compra.

### **Número de Pedido (Order Number)**

Identificador único y legible para el cliente, generado automáticamente.

**Formato:** `{PREFIX}-{TIMESTAMP}-{SEQUENCE}`
**Ejemplo:** `ORD-20250904-001`

## 🏠 Direcciones y Envío

### **Dirección (Address)**

Información de ubicación física para facturación o envío.

**Componentes:**

-  `line1`: Dirección principal
-  `line2`: Información adicional (opcional)
-  `city`: Ciudad
-  `state`: Estado/Provincia
-  `country`: País
-  `zip`: Código postal

### **Dirección de Facturación (Billing Address)**

Dirección asociada al método de pago del cliente.

### **Dirección de Envío (Shipping Address)**

Dirección donde se entregará el pedido físicamente.

## 💰 Precios e Impuestos

### **Precio Base (Base Price)**

Precio del producto sin incluir impuestos.

### **Precio con Impuestos (Price with Tax)**

Precio final que incluye todos los impuestos aplicables.

**Cálculo:** `precio_con_impuestos = precio_base × (1 + tax_rate / 100)`

### **IVA/Impuesto (Tax)**

Porcentaje aplicado sobre el precio base.

**Configuración:**

-  Global: `tax_rate` en configuración
-  Por producto: `tax_rate` en modelo Product
-  Por variante: Hereda del producto padre

### **Moneda (Currency)**

Unidad monetaria utilizada en precios y transacciones.

**Soportadas:**

-  `EUR`: Euro (€)
-  `USD`: Dólar estadounidense ($)

## 🏷️ Taxonomías y Categorización

### **Taxonomía (Taxonomy)**

Sistema jerárquico de clasificación proporcionado por `aliziodev/laravel-taxonomy`.

### **Categoría (Category)**

Clasificación principal de productos para navegación y filtrado.

**Tipo de taxonomía:** `lbcdev-ecommerce-category`
**Prefijo de slug:** `cat--`

### **Etiqueta (Tag)**

Marcador descriptivo para características específicas o filtros.

**Tipo de taxonomía:** `lbcdev-ecommerce-tag`
**Prefijo de slug:** `tag--`

## 🔧 Configuración y Sistema

### **Driver de Carrito**

Estrategia de almacenamiento para datos del carrito:

-  **Session**: Solo en sesión del navegador
-  **Database**: Persistente en base de datos
-  **Hybrid**: Sesión para anónimos, base de datos para usuarios autenticados

### **Limpieza Automática (Auto Cleanup)**

Proceso automatizado que elimina carritos expirados según configuración.

**Configuración:** `expires_after_days` en config del carrito

## 📊 Eventos del Sistema

### **OrderCreated**

Se dispara cuando se crea un nuevo pedido exitosamente.

### **OrderStatusChanged**

Se dispara cuando cambia el estado de un pedido.

**Payload:** Pedido, estado anterior, estado nuevo

### **UserLoggedIn**

Se dispara cuando un usuario se autentica, para fusionar carritos anónimos.

---

## Convenciones de Nomenclatura

### **Modelos**

-  Singular, PascalCase: `Product`, `Order`, `CartItem`
-  Tablas en plural, snake_case: `lbcdev_ecommerce_products`

### **Eventos**

-  Verbo en pasado: `OrderCreated`, `OrderStatusChanged`
-  Namespace: `LBCDev\Ecommerce\Events`

### **Servicios**

-  Sufijo "Service": `CartService`
-  Singleton en contenedor de servicios

### **Contratos**

-  Sufijo "Interface": `CartStorageInterface`
-  Namespace: `LBCDev\Ecommerce\Contracts`

---

_Este glosario se mantiene actualizado manualmente. Si introduces nuevos conceptos, actualiza este documento._
