<?php

namespace LBCDev\Ecommerce\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateConfigDocs extends Command
{
    protected $signature = 'lbcdev:docs-config';
    protected $description = 'Generate configuration documentation from config file';

    public function handle(): int
    {
        $this->info('Generating configuration documentation...');

        $configPath = config_path('lbcdev-ecommerce.php');
        $outputPath = base_path('docs/reference/config.md');

        if (!File::exists($configPath)) {
            $this->error("Configuration file not found at: {$configPath}");
            $this->info('Please publish the configuration first: php artisan vendor:publish --tag="lbcdev-ecommerce-config"');
            return 1;
        }

        // Load configuration
        $config = include $configPath;
        
        // Generate markdown content
        $markdown = $this->generateMarkdown($config);
        
        // Ensure directory exists
        File::ensureDirectoryExists(dirname($outputPath));
        
        // Write file
        File::put($outputPath, $markdown);
        
        $this->info("Configuration documentation generated at: {$outputPath}");
        
        return 0;
    }

    private function generateMarkdown(array $config): string
    {
        $markdown = "# Configuración del Paquete\n\n";
        $markdown .= "> 📝 **Nota**: Esta documentación se genera automáticamente desde `config/lbcdev-ecommerce.php`\n";
        $markdown .= "> \n";
        $markdown .= "> **Última actualización**: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        $markdown .= "## Archivo de Configuración\n\n";
        $markdown .= "El archivo de configuración se publica en `config/lbcdev-ecommerce.php` y contiene las siguientes opciones:\n\n";
        
        $markdown .= "```bash\n";
        $markdown .= "php artisan vendor:publish --tag=\"lbcdev-ecommerce-config\"\n";
        $markdown .= "```\n\n";
        
        $markdown .= "## Opciones de Configuración\n\n";
        
        $markdown .= $this->generateConfigSection($config);
        
        $markdown .= "\n## Variables de Entorno\n\n";
        $markdown .= "Las siguientes variables de entorno pueden ser utilizadas en tu archivo `.env`:\n\n";
        $markdown .= $this->generateEnvVariables($config);
        
        $markdown .= "\n---\n\n";
        $markdown .= "*Esta documentación se genera automáticamente. Para actualizarla ejecuta `php artisan lbcdev:docs-config`*\n";
        
        return $markdown;
    }

    private function generateConfigSection(array $config, string $prefix = '', int $level = 3): string
    {
        $markdown = '';
        
        foreach ($config as $key => $value) {
            $fullKey = $prefix ? "{$prefix}.{$key}" : $key;
            $heading = str_repeat('#', $level);
            
            if (is_array($value) && !$this->isAssociativeArray($value)) {
                // Simple array
                $markdown .= "{$heading} `{$fullKey}`\n\n";
                $markdown .= $this->getConfigDescription($key, $value);
                $markdown .= "**Tipo**: Array\n\n";
                $markdown .= "**Valores**: `" . implode('`, `', $value) . "`\n\n";
            } elseif (is_array($value)) {
                // Nested configuration
                $markdown .= "{$heading} `{$fullKey}`\n\n";
                $markdown .= $this->getConfigDescription($key, $value);
                $markdown .= $this->generateConfigSection($value, $fullKey, $level + 1);
            } else {
                // Simple value
                $markdown .= "{$heading} `{$fullKey}`\n\n";
                $markdown .= $this->getConfigDescription($key, $value);
                $markdown .= "**Tipo**: " . $this->getValueType($value) . "\n\n";
                $markdown .= "**Valor por defecto**: `" . $this->formatValue($value) . "`\n\n";
            }
        }
        
        return $markdown;
    }

    private function generateEnvVariables(array $config, string $prefix = ''): string
    {
        $envVars = [];
        $this->extractEnvVariables($config, $envVars);
        
        if (empty($envVars)) {
            return "No se encontraron variables de entorno configuradas.\n";
        }
        
        $markdown = "| Variable | Descripción | Valor por defecto |\n";
        $markdown .= "|----------|-------------|-------------------|\n";
        
        foreach ($envVars as $var => $default) {
            $description = $this->getEnvDescription($var);
            $markdown .= "| `{$var}` | {$description} | `{$default}` |\n";
        }
        
        return $markdown . "\n";
    }

    private function extractEnvVariables(array $config, array &$envVars): void
    {
        foreach ($config as $value) {
            if (is_string($value) && str_starts_with($value, 'env(')) {
                preg_match('/env\([\'"]([^\'",]+)[\'"](?:,\s*[\'"]?([^\'")]*)[\'"]?)?\)/', $value, $matches);
                if (isset($matches[1])) {
                    $envVars[$matches[1]] = $matches[2] ?? 'null';
                }
            } elseif (is_array($value)) {
                $this->extractEnvVariables($value, $envVars);
            }
        }
    }

    private function getConfigDescription(string $key, mixed $value): string
    {
        $descriptions = [
            'enabled' => 'Activa o desactiva completamente el paquete de e-commerce.',
            'currency' => 'Moneda por defecto utilizada en precios y transacciones.',
            'tax_rate' => 'Porcentaje de impuesto (IVA) aplicado por defecto.',
            'order_number_prefix' => 'Prefijo utilizado en la generación de números de pedido.',
            'cart' => 'Configuración del sistema de carrito de compras.',
            'driver' => 'Estrategia de almacenamiento del carrito (session, database, hybrid).',
            'session_key' => 'Clave utilizada para almacenar el carrito en la sesión.',
            'expires_after_days' => 'Días después de los cuales expiran los carritos persistentes.',
            'auto_cleanup' => 'Habilita la limpieza automática de carritos expirados.',
            'send_order_confirmation' => 'Envía emails de confirmación de pedidos automáticamente.',
            'admin_email' => 'Email del administrador para notificaciones.',
            'categories_taxonomy_type' => 'Tipo de taxonomía utilizado para categorías.',
            'tags_taxonomy_type' => 'Tipo de taxonomía utilizado para etiquetas.',
            'categories_table' => 'Tabla de base de datos utilizada para taxonomías.',
            'taxonomy_slug_prefixes' => 'Prefijos utilizados en los slugs de taxonomías.',
            'models' => 'Permite sobrescribir los modelos por defecto del paquete.',
        ];

        return isset($descriptions[$key]) ? $descriptions[$key] . "\n\n" : '';
    }

    private function getEnvDescription(string $envVar): string
    {
        $descriptions = [
            'ECOMMERCE_CURRENCY' => 'Moneda por defecto',
            'ECOMMERCE_TAX_RATE' => 'Porcentaje de impuesto por defecto',
            'ECOMMERCE_ORDER_PREFIX' => 'Prefijo de números de pedido',
            'ECOMMERCE_CART_DRIVER' => 'Driver de almacenamiento del carrito',
            'ECOMMERCE_CART_EXPIRES_DAYS' => 'Días de expiración del carrito',
            'ECOMMERCE_CART_AUTO_CLEANUP' => 'Limpieza automática de carritos',
            'ECOMMERCE_SEND_ORDER_CONFIRMATION' => 'Envío de confirmaciones de pedido',
            'ECOMMERCE_ADMIN_EMAIL' => 'Email del administrador',
        ];

        return $descriptions[$envVar] ?? 'Configuración del paquete';
    }

    private function isAssociativeArray(array $array): bool
    {
        return array_keys($array) !== range(0, count($array) - 1);
    }

    private function getValueType(mixed $value): string
    {
        return match (gettype($value)) {
            'boolean' => 'Boolean',
            'integer' => 'Integer',
            'double' => 'Float',
            'string' => 'String',
            'array' => 'Array',
            'NULL' => 'Null',
            default => 'Mixed'
        };
    }

    private function formatValue(mixed $value): string
    {
        return match (gettype($value)) {
            'boolean' => $value ? 'true' : 'false',
            'NULL' => 'null',
            'string' => "'{$value}'",
            default => (string) $value
        };
    }
}
