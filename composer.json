{"name": "lbcdev/ecommerce", "description": "Ecommerce package for Laravel", "type": "library", "license": "MIT", "autoload": {"psr-4": {"LBCDev\\Ecommerce\\": "src/", "LBCDev\\Ecommerce\\Database\\Factories\\": "database/factories/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Tests\\Fakes\\": "tests/Fakes/"}}, "extra": {"laravel": {"providers": ["LBCDev\\Ecommerce\\EcommerceServiceProvider"]}}, "require": {"php": "^8.1", "illuminate/support": "^10.0|^11.0|^12.0", "illuminate/contracts": "^10.0|^11.0|^12.0", "aliziodev/laravel-taxonomy": "^2.5"}, "require-dev": {"phpunit/phpunit": "^11.0", "orchestra/testbench": "^10.4"}, "scripts": {"docs:config": "php artisan lbcdev:docs-config", "docs:events": "php artisan lbcdev:docs-events", "docs:contracts": "php artisan lbcdev:docs-contracts", "docs:build": ["@docs:config", "@docs:events", "@docs:contracts"], "test": "phpunit"}, "minimum-stability": "dev", "prefer-stable": true}