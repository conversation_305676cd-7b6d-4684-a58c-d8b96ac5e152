<?php

namespace LBCDev\Ecommerce\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use ReflectionClass;
use ReflectionProperty;

class GenerateEventsDocs extends Command
{
    protected $signature = 'lbcdev:docs-events';
    protected $description = 'Generate events documentation from event classes';

    public function handle(): int
    {
        $this->info('Generating events documentation...');

        $eventsPath = base_path('src/Events');
        $outputPath = base_path('docs/reference/events.md');

        if (!File::exists($eventsPath)) {
            $this->error("Events directory not found at: {$eventsPath}");
            return 1;
        }

        // Scan for event classes
        $eventClasses = $this->scanEventClasses($eventsPath);
        
        if (empty($eventClasses)) {
            $this->warn('No event classes found.');
            return 0;
        }

        // Generate markdown content
        $markdown = $this->generateMarkdown($eventClasses);
        
        // Ensure directory exists
        File::ensureDirectoryExists(dirname($outputPath));
        
        // Write file
        File::put($outputPath, $markdown);
        
        $this->info("Events documentation generated at: {$outputPath}");
        $this->info("Found " . count($eventClasses) . " event classes.");
        
        return 0;
    }

    private function scanEventClasses(string $path): array
    {
        $eventClasses = [];
        $files = File::allFiles($path);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = $this->getClassNameFromFile($file->getPathname());
                if ($className && class_exists($className)) {
                    $eventClasses[] = $className;
                }
            }
        }

        return $eventClasses;
    }

    private function getClassNameFromFile(string $filePath): ?string
    {
        $content = File::get($filePath);
        
        // Extract namespace
        preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches);
        $namespace = $namespaceMatches[1] ?? '';
        
        // Extract class name
        preg_match('/class\s+(\w+)/', $content, $classMatches);
        $className = $classMatches[1] ?? '';
        
        if ($namespace && $className) {
            return $namespace . '\\' . $className;
        }
        
        return null;
    }

    private function generateMarkdown(array $eventClasses): string
    {
        $markdown = "# Sistema de Eventos\n\n";
        $markdown .= "> 📝 **Nota**: Esta documentación se genera automáticamente desde las clases de eventos\n";
        $markdown .= "> \n";
        $markdown .= "> **Última actualización**: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        $markdown .= "## Introducción\n\n";
        $markdown .= "El paquete LBCDev Ecommerce utiliza el sistema de eventos de Laravel para permitir la extensibilidad y el desacoplamiento entre módulos.\n\n";
        
        $markdown .= "## Eventos Disponibles\n\n";
        
        foreach ($eventClasses as $eventClass) {
            $markdown .= $this->generateEventSection($eventClass);
        }
        
        $markdown .= "## Escuchar Eventos\n\n";
        $markdown .= $this->generateListenerExamples();
        
        $markdown .= "\n---\n\n";
        $markdown .= "*Esta documentación se genera automáticamente. Para actualizarla ejecuta `php artisan lbcdev:docs-events`*\n";
        
        return $markdown;
    }

    private function generateEventSection(string $eventClass): string
    {
        $reflection = new ReflectionClass($eventClass);
        $className = $reflection->getShortName();
        
        $markdown = "### `{$className}`\n\n";
        $markdown .= "**Clase completa**: `{$eventClass}`\n\n";
        
        // Get class docblock
        $docComment = $reflection->getDocComment();
        if ($docComment) {
            $description = $this->parseDocComment($docComment);
            if ($description) {
                $markdown .= "**Descripción**: {$description}\n\n";
            }
        } else {
            $markdown .= $this->getEventDescription($className);
        }
        
        // Get constructor parameters (event payload)
        $constructor = $reflection->getConstructor();
        if ($constructor) {
            $parameters = $constructor->getParameters();
            if (!empty($parameters)) {
                $markdown .= "**Payload**:\n\n";
                foreach ($parameters as $parameter) {
                    $type = $parameter->getType() ? $parameter->getType()->getName() : 'mixed';
                    $name = $parameter->getName();
                    $markdown .= "- `{$name}` ({$type})\n";
                }
                $markdown .= "\n";
            }
        }
        
        // Get public properties
        $properties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);
        if (!empty($properties)) {
            $markdown .= "**Propiedades públicas**:\n\n";
            foreach ($properties as $property) {
                $type = $property->getType() ? $property->getType()->getName() : 'mixed';
                $name = $property->getName();
                $markdown .= "- `{$name}` ({$type})\n";
            }
            $markdown .= "\n";
        }
        
        // Usage example
        $markdown .= "**Ejemplo de uso**:\n\n";
        $markdown .= "```php\n";
        $markdown .= $this->generateUsageExample($eventClass, $className);
        $markdown .= "```\n\n";
        
        return $markdown;
    }

    private function parseDocComment(string $docComment): ?string
    {
        // Remove /** and */ and extract first line of description
        $lines = explode("\n", $docComment);
        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B/*");
            if (!empty($line) && !str_starts_with($line, '@')) {
                return $line;
            }
        }
        return null;
    }

    private function getEventDescription(string $className): string
    {
        $descriptions = [
            'OrderCreated' => '**Descripción**: Se dispara cuando se crea un nuevo pedido exitosamente.',
            'OrderStatusChanged' => '**Descripción**: Se dispara cuando cambia el estado de un pedido.',
            'UserLoggedIn' => '**Descripción**: Se dispara cuando un usuario se autentica, utilizado para fusionar carritos anónimos.',
            'CartUpdated' => '**Descripción**: Se dispara cuando se actualiza el contenido del carrito.',
            'ProductCreated' => '**Descripción**: Se dispara cuando se crea un nuevo producto.',
            'ProductUpdated' => '**Descripción**: Se dispara cuando se actualiza un producto existente.',
        ];

        return $descriptions[$className] ?? "**Descripción**: Evento del sistema de e-commerce.";
    }

    private function generateUsageExample(string $eventClass, string $className): string
    {
        $examples = [
            'OrderCreated' => "// Escuchar el evento\nEvent::listen(OrderCreated::class, function (OrderCreated \$event) {\n    \$order = \$event->order;\n    // Enviar email de confirmación\n    Mail::to(\$order->user)->send(new OrderConfirmation(\$order));\n});",
            'OrderStatusChanged' => "// Escuchar el evento\nEvent::listen(OrderStatusChanged::class, function (OrderStatusChanged \$event) {\n    \$order = \$event->order;\n    \$previousStatus = \$event->previousStatus;\n    \$newStatus = \$event->newStatus;\n    \n    // Notificar al cliente del cambio de estado\n    if (\$newStatus === 'shipped') {\n        Mail::to(\$order->user)->send(new OrderShipped(\$order));\n    }\n});",
            'UserLoggedIn' => "// Este evento se maneja automáticamente por el paquete\n// para fusionar carritos anónimos con el carrito del usuario",
        ];

        return $examples[$className] ?? "// Escuchar el evento\nEvent::listen({$className}::class, function ({$className} \$event) {\n    // Tu lógica aquí\n});";
    }

    private function generateListenerExamples(): string
    {
        return "### Registrar Listeners\n\n" .
               "Puedes registrar listeners para los eventos en tu `EventServiceProvider`:\n\n" .
               "```php\n" .
               "// app/Providers/EventServiceProvider.php\n" .
               "protected \$listen = [\n" .
               "    \\LBCDev\\Ecommerce\\Events\\OrderCreated::class => [\n" .
               "        \\App\\Listeners\\SendOrderConfirmation::class,\n" .
               "        \\App\\Listeners\\UpdateInventory::class,\n" .
               "    ],\n" .
               "    \\LBCDev\\Ecommerce\\Events\\OrderStatusChanged::class => [\n" .
               "        \\App\\Listeners\\NotifyCustomer::class,\n" .
               "    ],\n" .
               "];\n" .
               "```\n\n" .
               "O usar closures directamente:\n\n" .
               "```php\n" .
               "// En un ServiceProvider\n" .
               "Event::listen(\n" .
               "    \\LBCDev\\Ecommerce\\Events\\OrderCreated::class,\n" .
               "    function (\\LBCDev\\Ecommerce\\Events\\OrderCreated \$event) {\n" .
               "        // Tu lógica aquí\n" .
               "    }\n" .
               ");\n" .
               "```\n";
    }
}
