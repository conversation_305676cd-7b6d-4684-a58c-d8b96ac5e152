<?php

namespace LBC<PERSON>ev\Ecommerce\Providers;

use LBCDev\Ecommerce\Contracts\CartStorageInterface;
use LBCDev\Ecommerce\Services\CartStorage\SessionCartStorage;
use LBCDev\Ecommerce\Services\CartStorage\DatabaseCartStorage;
use LBCDev\Ecommerce\Services\CartStorage\HybridCartStorage;

use Illuminate\Support\ServiceProvider;
use LBCDev\Ecommerce\Services\CartService;

class CartServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Registrar storages individuales
        $this->app->singleton(SessionCartStorage::class);
        $this->app->singleton(DatabaseCartStorage::class);
        $this->app->singleton(HybridCartStorage::class);

        // Registrar storage interface según configuración
        $this->app->singleton(CartStorageInterface::class, function ($app) {
            $driver = config('ecommerce.cart.driver', 'session');

            return match ($driver) {
                'session' => $app->make(SessionCartStorage::class),
                'database' => $app->make(DatabaseCartStorage::class),
                'hybrid' => $app->make(HybridCartStorage::class),
                default => throw new \InvalidArgumentException("Unsupported cart driver: {$driver}"),
            };
        });

        // Registrar CartManager
        $this->app->singleton(CartService::class);

        // Alias para facilitar uso
        $this->app->alias(CartService::class, 'ecommerce.cart');
    }

    public function boot(): void
    {
        // Registrar comandos
        if ($this->app->runningInConsole()) {
            $this->commands([
                \LBCDev\Ecommerce\Console\Commands\CleanupExpiredCarts::class,
                \LBCDev\Ecommerce\Console\Commands\GenerateConfigDocs::class,
                \LBCDev\Ecommerce\Console\Commands\GenerateEventsDocs::class,
                \LBCDev\Ecommerce\Console\Commands\GenerateContractsDocs::class,
            ]);
        }
    }
}
