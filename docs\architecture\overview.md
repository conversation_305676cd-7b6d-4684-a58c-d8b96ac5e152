# Arquitectura del Sistema

## Visión General

LBCDev Ecommerce es un paquete modular para Laravel que implementa funcionalidades de comercio electrónico siguiendo principios de Domain-Driven Design (DDD) y arquitectura desacoplada.

## Módulos Principales

### 🛒 **<PERSON><PERSON> (Cart)**

-  **Responsabilidad**: Gestión de carritos de compra persistentes
-  **Componentes**:
   -  `CartService` - Lógica de negocio del carrito
   -  `CartStorageInterface` - Abstracción de almacenamiento
   -  Implementaciones: Session, Database, Hybrid
-  **Eventos**: Carrito actualizado, items añadidos/eliminados

### 📦 **Catálogo (Catalog)**

-  **Responsabilidad**: Gestión de productos y variantes
-  **Componentes**:
   -  `Product` - Modelo principal de producto
   -  `ProductVariant` - Variantes con SKU y precios específicos
   -  `ProductLinkable` - Sistema de enlaces polimórficos
-  **Características**: SKU obligatorio, precios con impuestos, stock básico

### 🏷️ **Taxonomías (Taxonomies)**

-  **Responsabilidad**: Categorización y etiquetado
-  **Integración**: `aliziodev/laravel-taxonomy`
-  **Traits**: `HasCategories`, `HasTags`
-  **Configuración**: Tipos de taxonomía personalizables

### 📋 **Pedidos (Orders)**

-  **Responsabilidad**: Gestión del ciclo de vida de pedidos
-  **Componentes**:
   -  `Order` - Modelo principal de pedido
   -  `OrderItem` - Items individuales del pedido
   -  `Address` - Direcciones de facturación y envío
-  **Estados**: Pending → Processing → Shipped → Delivered
-  **Eventos**: OrderCreated, OrderStatusChanged

### 💰 **Precios e Impuestos**

-  **Responsabilidad**: Cálculo de precios y aplicación de impuestos
-  **Características**:
   -  Precios base y con impuestos
   -  Configuración de IVA por defecto
   -  Soporte multi-moneda (EUR, USD)

## Bounded Contexts

```mermaid
graph TB
    subgraph "Catalog Context"
        P[Product]
        PV[ProductVariant]
        PL[ProductLinkable]
    end

    subgraph "Cart Context"
        C[Cart]
        CI[CartItem]
        CS[CartService]
    end

    subgraph "Order Context"
        O[Order]
        OI[OrderItem]
        A[Address]
    end

    subgraph "Taxonomy Context"
        T[Taxonomy]
        HC[HasCategories]
        HT[HasTags]
    end

    P --> PV
    P --> PL
    P --> T
    C --> CI
    CI --> PV
    O --> OI
    O --> A
    OI --> PV
```

## Patrones de Diseño Utilizados

### 1. **Strategy Pattern**

-  **Uso**: Almacenamiento de carrito (Session, Database, Hybrid)
-  **Beneficio**: Flexibilidad para cambiar estrategia de persistencia

### 2. **Observer Pattern**

-  **Uso**: Sistema de eventos (OrderCreated, OrderStatusChanged)
-  **Beneficio**: Desacoplamiento entre módulos

### 3. **Polymorphic Relations**

-  **Uso**: ProductLinkable para asociar productos con cualquier modelo
-  **Beneficio**: Extensibilidad sin modificar el core

### 4. **Service Layer**

-  **Uso**: CartService encapsula lógica de negocio
-  **Beneficio**: Separación de responsabilidades

## Dependencias Externas

-  **Laravel Framework**: ^10.0|^11.0|^12.0
-  **aliziodev/laravel-taxonomy**: ^2.5 (Gestión de taxonomías)
-  **PHP**: ^8.1

## Configuración y Extensibilidad

El paquete es altamente configurable a través de:

-  **Archivo de configuración**: `config/lbcdev-ecommerce.php`
-  **Modelos personalizables**: Permite sobrescribir modelos por defecto
-  **Eventos**: Sistema de hooks para personalización
-  **Vistas publicables**: Personalización de UI
-  **Comandos Artisan**: Utilidades de mantenimiento

## Diagrama de Dependencias

> 📊 **Diagrama generado automáticamente**
>
> Última actualización: _Se actualizará con `composer docs:graph`_
>
> ![Diagrama de dependencias](diagrams/dependencies.svg)

---

_Esta documentación se genera automáticamente. Para actualizarla ejecuta `composer docs:build`_
