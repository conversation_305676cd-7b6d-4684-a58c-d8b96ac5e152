<?php

namespace LBCDev\Ecommerce\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use ReflectionClass;
use ReflectionMethod;

class GenerateContractsDocs extends Command
{
    protected $signature = 'lbcdev:docs-contracts';
    protected $description = 'Generate contracts documentation from interface classes';

    public function handle(): int
    {
        $this->info('Generating contracts documentation...');

        $contractsPath = base_path('src/Contracts');
        $outputPath = base_path('docs/reference/contracts.md');

        if (!File::exists($contractsPath)) {
            $this->error("Contracts directory not found at: {$contractsPath}");
            return 1;
        }

        // Scan for contract classes
        $contractClasses = $this->scanContractClasses($contractsPath);
        
        if (empty($contractClasses)) {
            $this->warn('No contract classes found.');
            return 0;
        }

        // Generate markdown content
        $markdown = $this->generateMarkdown($contractClasses);
        
        // Ensure directory exists
        File::ensureDirectoryExists(dirname($outputPath));
        
        // Write file
        File::put($outputPath, $markdown);
        
        $this->info("Contracts documentation generated at: {$outputPath}");
        $this->info("Found " . count($contractClasses) . " contract classes.");
        
        return 0;
    }

    private function scanContractClasses(string $path): array
    {
        $contractClasses = [];
        $files = File::allFiles($path);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = $this->getClassNameFromFile($file->getPathname());
                if ($className && interface_exists($className)) {
                    $contractClasses[] = $className;
                }
            }
        }

        return $contractClasses;
    }

    private function getClassNameFromFile(string $filePath): ?string
    {
        $content = File::get($filePath);
        
        // Extract namespace
        preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches);
        $namespace = $namespaceMatches[1] ?? '';
        
        // Extract interface name
        preg_match('/interface\s+(\w+)/', $content, $interfaceMatches);
        $interfaceName = $interfaceMatches[1] ?? '';
        
        if ($namespace && $interfaceName) {
            return $namespace . '\\' . $interfaceName;
        }
        
        return null;
    }

    private function generateMarkdown(array $contractClasses): string
    {
        $markdown = "# Contratos e Interfaces\n\n";
        $markdown .= "> 📝 **Nota**: Esta documentación se genera automáticamente desde las interfaces del paquete\n";
        $markdown .= "> \n";
        $markdown .= "> **Última actualización**: " . now()->format('Y-m-d H:i:s') . "\n\n";
        
        $markdown .= "## Introducción\n\n";
        $markdown .= "Los contratos definen las interfaces públicas del paquete LBCDev Ecommerce. ";
        $markdown .= "Estas interfaces permiten la extensibilidad y el intercambio de implementaciones sin modificar el código del paquete.\n\n";
        
        $markdown .= "## Contratos Disponibles\n\n";
        
        foreach ($contractClasses as $contractClass) {
            $markdown .= $this->generateContractSection($contractClass);
        }
        
        $markdown .= "## Implementar Contratos Personalizados\n\n";
        $markdown .= $this->generateImplementationExamples();
        
        $markdown .= "\n---\n\n";
        $markdown .= "*Esta documentación se genera automáticamente. Para actualizarla ejecuta `php artisan lbcdev:docs-contracts`*\n";
        
        return $markdown;
    }

    private function generateContractSection(string $contractClass): string
    {
        $reflection = new ReflectionClass($contractClass);
        $interfaceName = $reflection->getShortName();
        
        $markdown = "### `{$interfaceName}`\n\n";
        $markdown .= "**Interface completa**: `{$contractClass}`\n\n";
        
        // Get interface docblock
        $docComment = $reflection->getDocComment();
        if ($docComment) {
            $description = $this->parseDocComment($docComment);
            if ($description) {
                $markdown .= "**Descripción**: {$description}\n\n";
            }
        } else {
            $markdown .= $this->getContractDescription($interfaceName);
        }
        
        // Get methods
        $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
        if (!empty($methods)) {
            $markdown .= "**Métodos**:\n\n";
            foreach ($methods as $method) {
                $markdown .= $this->generateMethodDocumentation($method);
            }
        }
        
        // Get implementations
        $implementations = $this->findImplementations($contractClass);
        if (!empty($implementations)) {
            $markdown .= "**Implementaciones incluidas**:\n\n";
            foreach ($implementations as $implementation) {
                $shortName = (new ReflectionClass($implementation))->getShortName();
                $markdown .= "- `{$shortName}` (`{$implementation}`)\n";
            }
            $markdown .= "\n";
        }
        
        // Usage example
        $markdown .= "**Ejemplo de uso**:\n\n";
        $markdown .= "```php\n";
        $markdown .= $this->generateUsageExample($contractClass, $interfaceName);
        $markdown .= "```\n\n";
        
        return $markdown;
    }

    private function generateMethodDocumentation(ReflectionMethod $method): string
    {
        $methodName = $method->getName();
        $parameters = [];
        
        foreach ($method->getParameters() as $param) {
            $type = $param->getType() ? $param->getType()->getName() : 'mixed';
            $name = $param->getName();
            $optional = $param->isOptional() ? '?' : '';
            $parameters[] = "{$optional}{$type} \${$name}";
        }
        
        $returnType = $method->getReturnType() ? $method->getReturnType()->getName() : 'mixed';
        $paramString = implode(', ', $parameters);
        
        $markdown = "#### `{$methodName}({$paramString}): {$returnType}`\n\n";
        
        // Get method docblock
        $docComment = $method->getDocComment();
        if ($docComment) {
            $description = $this->parseDocComment($docComment);
            if ($description) {
                $markdown .= "{$description}\n\n";
            }
        }
        
        return $markdown;
    }

    private function parseDocComment(string $docComment): ?string
    {
        // Remove /** and */ and extract first line of description
        $lines = explode("\n", $docComment);
        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B/*");
            if (!empty($line) && !str_starts_with($line, '@')) {
                return $line;
            }
        }
        return null;
    }

    private function getContractDescription(string $interfaceName): string
    {
        $descriptions = [
            'CartStorageInterface' => '**Descripción**: Define los métodos para el almacenamiento y gestión de carritos de compra.',
            'PaymentGatewayInterface' => '**Descripción**: Define los métodos para integrar pasarelas de pago.',
            'ShippingProviderInterface' => '**Descripción**: Define los métodos para proveedores de envío.',
            'TaxCalculatorInterface' => '**Descripción**: Define los métodos para el cálculo de impuestos.',
        ];

        return $descriptions[$interfaceName] ?? "**Descripción**: Contrato del sistema de e-commerce.";
    }

    private function findImplementations(string $contractClass): array
    {
        // This is a simplified implementation
        // In a real scenario, you might want to scan all classes and check if they implement the interface
        $knownImplementations = [
            'LBCDev\Ecommerce\Contracts\CartStorageInterface' => [
                'LBCDev\Ecommerce\Services\CartStorage\SessionCartStorage',
                'LBCDev\Ecommerce\Services\CartStorage\DatabaseCartStorage',
                'LBCDev\Ecommerce\Services\CartStorage\HybridCartStorage',
            ],
        ];

        return $knownImplementations[$contractClass] ?? [];
    }

    private function generateUsageExample(string $contractClass, string $interfaceName): string
    {
        $examples = [
            'CartStorageInterface' => "// Obtener implementación desde el contenedor\n\$cartStorage = app(CartStorageInterface::class);\n\n// Usar los métodos del contrato\n\$cart = \$cartStorage->getCart('user-123');\n\$cartStorage->saveCart('user-123', \$cart);\n\$cartStorage->clearCart('user-123');",
        ];

        return $examples[$interfaceName] ?? "// Obtener implementación desde el contenedor\n\$implementation = app({$interfaceName}::class);\n\n// Usar los métodos definidos en el contrato";
    }

    private function generateImplementationExamples(): string
    {
        return "### Crear Implementaciones Personalizadas\n\n" .
               "Puedes crear tus propias implementaciones de los contratos:\n\n" .
               "```php\n" .
               "<?php\n\n" .
               "namespace App\\Services;\n\n" .
               "use LBCDev\\Ecommerce\\Contracts\\CartStorageInterface;\n" .
               "use LBCDev\\Ecommerce\\Models\\Cart;\n\n" .
               "class RedisCartStorage implements CartStorageInterface\n" .
               "{\n" .
               "    public function getCart(string \$identifier): Cart\n" .
               "    {\n" .
               "        // Tu implementación usando Redis\n" .
               "    }\n\n" .
               "    public function saveCart(string \$identifier, Cart \$cart): void\n" .
               "    {\n" .
               "        // Tu implementación usando Redis\n" .
               "    }\n\n" .
               "    // ... otros métodos\n" .
               "}\n" .
               "```\n\n" .
               "Luego registrar tu implementación en un ServiceProvider:\n\n" .
               "```php\n" .
               "// En un ServiceProvider\n" .
               "\$this->app->bind(\n" .
               "    \\LBCDev\\Ecommerce\\Contracts\\CartStorageInterface::class,\n" .
               "    \\App\\Services\\RedisCartStorage::class\n" .
               ");\n" .
               "```\n";
    }
}
